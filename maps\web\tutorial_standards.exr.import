[remap]

importer="2d_array_texture"
type="CompressedTexture2DArray"
uid="uid://co1o3igmlaet3"
path.bptc="res://.godot/imported/tutorial_standards.exr-49fdabeb6dafca96611c5996654b2828.bptc.ctexarray"
metadata={
"imported_formats": ["s3tc_bptc"],
"vram_texture": true
}

[deps]

source_file="res://maps/web/tutorial_standards.exr"
dest_files=["res://.godot/imported/tutorial_standards.exr-49fdabeb6dafca96611c5996654b2828.bptc.ctexarray"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/channel_pack=1
mipmaps/generate=false
mipmaps/limit=-1
slices/horizontal=1
slices/vertical=1
