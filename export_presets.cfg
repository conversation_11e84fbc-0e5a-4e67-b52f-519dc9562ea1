[preset.0]

name="Windows Desktop"
platform="Windows Desktop"
runnable=true
advanced_options=true
dedicated_server=false
custom_features=""
export_filter="all_resources"
include_filter=""
exclude_filter=""
export_path="build/stiletto-proto072325.exe"
patches=PackedStringArray()
encryption_include_filters=""
encryption_exclude_filters=""
seed=0
encrypt_pck=false
encrypt_directory=false
script_export_mode=2

[preset.0.options]

custom_template/debug=""
custom_template/release=""
debug/export_console_wrapper=1
binary_format/embed_pck=true
texture_format/s3tc_bptc=true
texture_format/etc2_astc=false
binary_format/architecture="x86_64"
codesign/enable=false
codesign/timestamp=true
codesign/timestamp_server_url=""
codesign/digest_algorithm=1
codesign/description=""
codesign/custom_options=PackedStringArray()
application/modify_resources=true
application/icon=""
application/console_wrapper_icon=""
application/icon_interpolation=4
application/file_version=""
application/product_version=""
application/company_name="Cotton Softworks"
application/product_name="Stiletto"
application/file_description=""
application/copyright="2025 Cotton Softworks"
application/trademarks=""
application/export_angle=1
application/export_d3d12=1
application/d3d12_agility_sdk_multiarch=true
ssh_remote_deploy/enabled=false
ssh_remote_deploy/host="user@host_ip"
ssh_remote_deploy/port="22"
ssh_remote_deploy/extra_args_ssh=""
ssh_remote_deploy/extra_args_scp=""
ssh_remote_deploy/run_script="Expand-Archive -LiteralPath '{temp_dir}\\{archive_name}' -DestinationPath '{temp_dir}'
$action = New-ScheduledTaskAction -Execute '{temp_dir}\\{exe_name}' -Argument '{cmd_args}'
$trigger = New-ScheduledTaskTrigger -Once -At 00:00
$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
$task = New-ScheduledTask -Action $action -Trigger $trigger -Settings $settings
Register-ScheduledTask godot_remote_debug -InputObject $task -Force:$true
Start-ScheduledTask -TaskName godot_remote_debug
while (Get-ScheduledTask -TaskName godot_remote_debug | ? State -eq running) { Start-Sleep -Milliseconds 100 }
Unregister-ScheduledTask -TaskName godot_remote_debug -Confirm:$false -ErrorAction:SilentlyContinue"
ssh_remote_deploy/cleanup_script="Stop-ScheduledTask -TaskName godot_remote_debug -ErrorAction:SilentlyContinue
Unregister-ScheduledTask -TaskName godot_remote_debug -Confirm:$false -ErrorAction:SilentlyContinue
Remove-Item -Recurse -Force '{temp_dir}'"
export/split_pcks=false

[preset.1]

name="Web"
platform="Web"
runnable=true
advanced_options=true
dedicated_server=false
custom_features=""
export_filter="scenes"
export_files=PackedStringArray("res://addons/prototype_mini_bundle/prototype_purple.png", "res://AudioMixerVisualizer/AudioMixerDebugFont.tres", "res://AudioMixerVisualizer/AudioMixerDebugVisualization.gd", "res://AudioMixerVisualizer/AudioMixerMeterDrawer.gd", "res://AudioMixerVisualizer/AudioMixerVolumeDrawer.gd", "res://AudioMixerVisualizer/BarlowCondensed-Regular.ttf", "res://assets/world/spin.gd", "res://addons/prototype_mini_bundle/prototype_red.png", "res://assets/world/illcnn6e.png", "res://addons/SunshineVolumetricClouds/CloudsMatAltLightingHighQuality.tres", "res://assets/ui/tplogo_wide.png", "res://materials/wireframe_gun.gdshader", "res://materials/wireframe.gdshader", "res://materials/PXL_20240222_140259222.jpg", "res://materials/miy760ay.png", "res://materials/Generated Image June 13, 2025 - 1_47AM.jpeg", "res://materials/Generated Image June 13, 2025 - 1_39AM.jpeg", "res://materials/Generated Image June 13, 2025 - 1_35AM.jpeg", "res://materials/models/props_se/uaz452/vehicle_gosnumber.vmt", "res://materials/models/props_se/uaz452/vehicle_gosnumber2.vmt", "res://materials/models/props_se/uaz452/vehicle_gosnumber1.vmt", "res://materials/models/props_se/uaz452/uaz452_wheel.vtf", "res://materials/models/props_se/uaz452/uaz452_wheel.vmt", "res://materials/models/props_se/uaz452/uaz452_color_05.vtf", "res://materials/models/props_se/uaz452/uaz452_color_05.vmt", "res://materials/models/props_se/uaz452/uaz452_color_04_env.vtf", "res://materials/models/props_se/uaz452/uaz452_color_04.vtf", "res://materials/models/props_se/uaz452/uaz452_color_04.vmt", "res://materials/models/props_se/uaz452/uaz452_color_03.vtf", "res://materials/models/props_se/uaz452/uaz452_color_03.vmt", "res://materials/models/props_se/uaz452/uaz452_color_02.vtf", "res://materials/models/props_se/uaz452/uaz452_color_02.vmt", "res://materials/models/props_se/uaz452/uaz452_color_01_env.vtf", "res://materials/models/props_se/uaz452/uaz452_color_01.vtf", "res://materials/models/props_se/uaz452/uaz452_color_01.vmt", "res://materials/models/props_se/uaz452/uaz452_base_env.vtf", "res://materials/models/props_se/uaz452/uaz452_base.vtf", "res://materials/models/props_se/uaz452/uaz452_base.vmt", "res://materials/models/props_se/uaz452/car_glass_04.vmt", "res://materials/models/props_se/uaz452/car_glass_03.vmt", "res://materials/models/props_se/uaz452/car_glass_02.vmt", "res://materials/models/props_se/uaz452/car_glass_01.vmt", "res://materials/lightmap/sandbox_night.lmbake", "res://materials/lightmap/sandbox_night.exr", "res://materials/lightmap/sandbox.lmbake", "res://materials/lightmap/sandbox.exr", "res://materials/env/skytest.tres", "res://materials/env/imgonline-com-ua-TextureSeamless-x49dqHe3bFPwhO3.jpg", "res://materials/env/default_env.tres", "res://materials/env/cubespheresky.gdshader", "res://materials/env/2560px-Sky_trainyard_01.jpg", "res://materials/dev/texture_12.vtf", "res://materials/dev/texture_12.vmt", "res://assets/snd/music/mad pierrot (sadesper record remix).ogg", "res://assets/snd/music/likeastaratdawnloop.ogg", "res://assets/snd/music/LikeAStarAtDawn.mp3", "res://assets/snd/music/hunters.ogg", "res://assets/snd/music/doyouknowwhereyourecomingfrominstrumental.wav", "res://assets/snd/music/BGM_00000006.mp3", "res://assets/snd/music/BGM_00000005.mp3", "res://assets/snd/music/ansum_doomloop.ogg", "res://assets/snd/effect/UI_23.wav", "res://assets/snd/ambient/sound_ambient_wind_crucial_wind_outdoors_1.wav", "res://assets/snd/ambient/sound_ambient_windwinterinside.wav", "res://assets/snd/ambient/sound_ambient_windwinter.wav", "res://assets/snd/ambient/sound_ambient_windlowandrustle.wav", "res://assets/snd/ambient/sound_ambient_lightwind.wav", "res://assets/snd/ambient/sound_ambient_lighthum.wav", "res://assets/snd/ambient/sound_ambient_italianradio.wav", "res://assets/snd/ambient/sound_ambient_atmosphere_underground_hall_loop1.wav", "res://assets/snd/ambient/sound_ambient_ambience_urban_rooftop_ambloop02.wav", "res://assets/snd/ambient/sound_ambient_ambience_mall_amb_loop.wav", "res://assets/snd/ambient/sound_ambience_wind_litegust.wav", "res://assets/snd/ambient/becoming sex.ogg", "res://assets/snd/ambient/becoming sex.mp3", "res://assets/snd/ambient/-1 - Fourth Chapter - On the Ground.mp3", "res://assets/player/wireframe_pretty.gdshader", "res://assets/player/wireframe2.tres", "res://addons/rakugo-nodes/examples/button_container_example.gd", "res://assets/player/v_revolver.gd", "res://assets/player/ss_player_viewmodel.tres", "res://addons/rakugo-nodes/nodes/ButtonContainer.gd", "res://assets/player/player_base.tres", "res://assets/player/sound/ThermosterLoop.ogg", "res://assets/player/sound/sePcCat02 [1].wav", "res://assets/player/sound/SamusShip_4.wav", "res://assets/player/sound/ReflectionPregame.ogg", "res://assets/player/sound/ReflectionLoop.ogg", "res://assets/player/sound/playerpain03.ogg", "res://assets/player/sound/playerpain02.ogg", "res://assets/player/sound/playerpain01.ogg", "res://assets/player/sound/playerpain00.ogg", "res://assets/player/sound/playerdeath.ogg", "res://assets/player/sound/mechav3.wav", "res://assets/player/sound/mechav2.wav", "res://assets/player/sound/mecha.wav", "res://assets/player/sound/madpierrot.ogg", "res://assets/player/sound/kickbackv3.ogg", "res://assets/player/sound/kickbackv2.wav", "res://assets/player/sound/kickbackv1.wav", "res://assets/player/sound/kickback.wav", "res://assets/player/sound/hook_retractv1.wav", "res://assets/player/sound/hook_retract.wav", "res://assets/player/sound/hook_bouncev1.wav", "res://assets/player/sound/hook_bounce.wav", "res://assets/player/sound/hardlanding.ogg", "res://assets/player/sound/gunshotv3.ogg", "res://assets/player/sound/gunshotv2.ogg", "res://assets/player/sound/gunshot.wav", "res://assets/player/sound/doom_zero.ogg", "res://assets/player/sound/doom_reaper.ogg", "res://assets/player/sound/doom_counter.ogg", "res://assets/player/sound/clangv2.wav", "res://assets/player/sound/clangv1.wav", "res://assets/player/sound/clang2.wav", "res://assets/player/sound/clang1.wav", "res://assets/player/sound/bigshot.ogg", "res://assets/player/sound/bell2.ogg", "res://assets/player/sound/bell1.ogg", "res://assets/player/sound/airdodge.ogg", "res://assets/player/sound/footsteps/tile.wav", "res://assets/player/sound/footsteps/tile4.wav", "res://assets/player/sound/footsteps/tile3.wav", "res://assets/player/sound/footsteps/tile2.wav", "res://assets/player/sound/footsteps/step.wav", "res://assets/player/sound/footsteps/step7.wav", "res://assets/player/sound/footsteps/step6.wav", "res://assets/player/sound/footsteps/step4.wav", "res://assets/player/sound/footsteps/step3.wav", "res://assets/player/sound/footsteps/step2.wav", "res://assets/player/sound/footsteps/footstep.wav", "res://assets/player/player/toon.gdshader", "res://assets/player/player/ss_player_viewmodel.tres", "res://addons/rakugo-nodes/nodes/ButtonContainer.svg", "res://assets/player/player/player_model_compass.gd", "res://assets/player/player/player.res", "res://addons/rakugo-nodes/utils/utils.gd", "res://assets/player/player/mtoon.gdshader", "res://addons/rakugo-nodes/rakugo_nodes.gd", "res://assets/player/player/compass.gd", "res://assets/player/player/textures/viewmodel.gdshader", "res://assets/player/player/textures/tip 4.png", "res://assets/player/player/textures/SokkyComm_Texture.png", "res://assets/player/player/textures/SokkyComm_512_Texture.png", "res://scripts/test_nextbot_scene.gd", "res://scripts/simple_nextbot_test.gd", "res://scripts/debug_autoload_issue.gd", "res://scripts/basic_test_no_autoload.gd", "res://scripts/enemy/nextbot_manager_simple.gd", "res://scripts/enemy/nextbot_manager.gd", "res://scripts/enemy/nextbot_interface.gd", "res://scripts/enemy/nextbot_event_responder.gd", "res://scripts/enemy/nextbot_component_interface.gd", "res://scripts/enemy/nextbot_action.gd", "res://scripts/enemy/manual_nextbot_test.gd", "res://scripts/enemy/enhanced_nextbot.gd", "res://scripts/enemy/enemy_nextbot.gd", "res://scripts/enemy/path/nextbot_path_follower.gd", "res://scripts/enemy/path/nextbot_path.gd", "res://scripts/enemy/interfaces/vision_interface.gd", "res://scripts/enemy/interfaces/locomotion_interface.gd", "res://scripts/enemy/interfaces/intention_interface.gd", "res://scripts/enemy/interfaces/body_interface.gd", "res://scripts/enemy/components/vision_component.gd", "res://scripts/enemy/components/locomotion_component.gd", "res://scripts/enemy/components/intention_component.gd", "res://scripts/enemy/components/enhanced_vision_component.gd", "res://scripts/enemy/components/enhanced_locomotion_component.gd", "res://scripts/enemy/components/enhanced_intention_component.gd", "res://scripts/enemy/components/enhanced_body_component.gd", "res://scripts/enemy/components/body_component.gd", "res://scripts/enemy/behaviors/seek_and_destroy_behavior.gd", "res://scripts/enemy/behaviors/retreat_behavior.gd", "res://scripts/enemy/behaviors/patrol_behavior.gd", "res://scripts/enemy/behaviors/idle_behavior.gd", "res://scripts/enemy/behaviors/behavior.gd", "res://scripts/enemy/behaviors/attack_behavior.gd", "res://scripts/enemy/actions/unstuck_action.gd", "res://scripts/enemy/actions/seek_and_destroy_action.gd", "res://scripts/enemy/actions/retreat_action.gd", "res://scripts/enemy/actions/patrol_action.gd", "res://scripts/enemy/actions/investigate_action.gd", "res://scripts/enemy/actions/idle_action.gd", "res://scripts/enemy/actions/death_action.gd", "res://scripts/enemy/actions/attack_action.gd", "res://addons/inspector_tabs/plugin.gd", "res://addons/map_list_export/map_list_export.gd", "res://addons/PCKManager/assets/DLCFilesItem/dlc_file_item.gd", "res://addons/SunshineVolumetricClouds/ExampleWorldEnvironment.tres", "res://addons/SunshineVolumetricClouds/BaseNoiseTexture.tres", "res://scenes/sky-hemisphere.gdshader", "res://addons/PCKManager/assets/pck_inspector.gd", "res://addons/PCKManager/assets/pck_manager.gd", "res://scenes/nextbottest.gd", "res://addons/PCKManager/PCKCustomizer/pck_customizer.gd", "res://scenes/multiplayer_test.gd", "res://addons/PCKManager/PCKDirAccess.gd", "res://scenes/multiplayer_game.gd", "res://addons/PCKManager/PCKManager.gd", "res://scenes/world/sky-hemisphere.gdshader", "res://scenes/world/portal.gd", "res://addons/PCKManager/pck_loader.gd", "res://scenes/ui/trialmenubutton.tres", "res://addons/prototype_mini_bundle/M_prototype_blue.tres", "res://scenes/ui/trialmenu.gd", "res://addons/prototype_mini_bundle/M_prototype_dark.tres", "res://addons/prototype_mini_bundle/M_prototype_green.tres", "res://scenes/ui/pause_menu.gd", "res://addons/prototype_mini_bundle/M_prototype_orange.tres", "res://scenes/ui/options_menu.gd", "res://addons/prototype_mini_bundle/M_prototype_purple.tres", "res://scenes/ui/multiplayer_menu_standalone.gd", "res://addons/prototype_mini_bundle/M_prototype_red.tres", "res://scenes/ui/multiplayer_menu.gd", "res://addons/prototype_mini_bundle/plugin.gd", "res://scenes/ui/mouse_sensitivity_control.gd", "res://addons/SunshineVolumetricClouds/CloudsController.gd", "res://scenes/ui/main_menu.gd", "res://addons/SunshineVolumetricClouds/CloudsMatMediumQuality.tres", "res://addons/SunshineVolumetricClouds/CloudsMatLowQuality.tres", "res://scenes/enemy/enemy_attack_system.gd", "res://scenes/enemy/base_enemy_parameters.gd", "res://scenes/enemy/base_enemy_collisions.gd", "res://scenes/enemy/base_enemy_attack_system_lite.gd", "res://scenes/enemy/base_enemy_attack_system.gd", "res://scenes/enemy/base_enemy_ai_lite.gd", "res://scenes/enemy/base_enemy_ai.gd", "res://addons/SunshineVolumetricClouds/CloudsMatHighQuality.tres", "res://scenes/enemy/base_enemy.gd", "res://scenes/enemy/quake/knight_reference.gd", "res://scenes/enemy/quake/knight_full.gd", "res://addons/SunshineVolumetricClouds/CloudsMatAltLightingMediumQuality.tres", "res://scenes/enemy/quake/knight.gd", "res://addons/prototype_mini_bundle/prototype_blue.png", "res://addons/prototype_mini_bundle/prototype_dark.png", "res://scenes/effects/spark_effect.gd", "res://addons/prototype_mini_bundle/prototype_green.png", "res://addons/prototype_mini_bundle/prototype_light.png", "res://scenes/effects/bullet_trail.gd", "res://addons/prototype_mini_bundle/prototype_orange.png", "res://resource/default_bus_layout.tres", "res://resource/system/map_list_generator.gd", "res://resource/system/map_browser.tscn", "res://resource/system/map_browser.gd", "res://resource/system/debug_camera_manager.gd", "res://resource/system/config/resolution_settings.tscn", "res://resource/system/config/resolution_settings.gd", "res://resource/system/audio/bgm_global.tscn", "res://resource/shaders/subviewsky.gdshader", "res://resource/shaders/sky.gdshader", "res://resource/shaders/dynamic_radius_decal.gdshader", "res://resource/scripts/wall_movement.gd", "res://resource/scripts/scene_music.gd", "res://resource/scripts/scene_loader.gd", "res://resource/scripts/scene_bgm_controller.gd", "res://resource/scripts/respawn.gd", "res://resource/scripts/resolution_manager.tscn", "res://resource/scripts/resolution_manager.gd", "res://resource/scripts/multiplayer_manager.gd", "res://resource/scripts/gamemaster.gd", "res://resource/scripts/crosshair_accuracy.gd", "res://resource/scripts/bell_hit_proxy.gd", "res://resource/scripts/bell_controller.gd", "res://resource/scripts/ammo_indicator_v2.gd", "res://resource/scripts/ammo_indicator.gd", "res://resource/scripts/weapons/weapon_manager_src.gd", "res://resource/scripts/weapons/pistol.gd", "res://resource/scripts/weapons/base_weapon.gd", "res://resource/scripts/PlayerCharacter/setup_gamepad_inputs.gd", "res://resource/scripts/PlayerCharacter/knockback_module.gd", "res://resource/scripts/PlayerCharacter/kickback_module.gd", "res://resource/scripts/PlayerCharacter/gamepad_controller.gd", "res://resource/modules/GroundAoeProjector.gd", "res://resource/entities/trigger_once.tscn", "res://resource/entities/trigger_once.gd", "res://resource/entities/portal_test.gd", "res://resource/entities/player_toon.tres", "res://resource/entities/obby00.tscn", "res://resource/entities/light_spot.tscn", "res://resource/entities/light_spot.gd", "res://resource/entities/light_environment.tscn", "res://resource/entities/light_environment.gd", "res://resource/entities/light.tscn", "res://resource/entities/light.gd", "res://resource/entities/killbox.gd", "res://resource/entities/goal.tscn", "res://resource/entities/goal.gd", "res://resource/entities/func_lod.tscn", "res://resource/entities/func_detail.tscn", "res://resource/entities/func_detail.gd", "res://resource/entities/func_brush.tscn", "res://resource/entities/enemy_spawner.tscn", "res://resource/entities/enemy_spawner.gd", "res://resource/entities/player/weapon_system.tscn", "res://resource/entities/player/weapon_system.gd", "res://resource/entities/player/wall_movement.gd", "res://resource/entities/player/v_revolver_3.gd", "res://resource/entities/player/viewmodel_lag.gd", "res://resource/entities/player/ss_player_multiplayer.tscn", "res://resource/entities/player/ss_player_multiplayer.gd", "res://resource/entities/player/ss_playerv2.tscn", "res://resource/entities/player/ss_player.tscn", "res://resource/entities/player/ss_player.gd", "res://resource/entities/player/speedometer.gd", "res://resource/entities/player/revolver.tscn", "res://resource/entities/player/projectile.tscn", "res://resource/entities/player/projectile.gd", "res://resource/entities/player/player_state.gd", "res://resource/entities/player/player_spawner.tscn", "res://resource/entities/player/player_spawner.gd", "res://resource/entities/player/player_health.gd", "res://resource/entities/player/multiplayer_weapon_sync.gd", "res://resource/entities/player/footstep_system.tscn", "res://resource/entities/player/bigshot_proj.tscn", "res://resource/entities/goal_routines/goal_routine_simple.gd", "res://materials/dev/texture_08.vtf", "res://materials/dev/texture_08.vmt", "res://assets/ui/tplogo.png", "res://assets/ui/stiletto04-godotasset.png", "res://assets/ui/04b09_labelsettings.tres", "res://assets/ui/IosevkaStiletto-ExtendedMedium.ttf", "res://assets/ui/IosevkaStiletto-ExtendedLight.ttf", "res://assets/ui/IosevkaStiletto-Condensed.ttf", "res://assets/ui/iosevka-latin-900-normal.ttf", "res://assets/ui/iosevka-latin-400-normal.ttf", "res://assets/ui/iosevka-latin-200-normal.ttf", "res://assets/ui/Helvetica Black Regular.otf", "res://assets/ui/04B_31__.TTF", "res://assets/ui/04B_24__.TTF", "res://assets/ui/04B_09__.TTF", "res://assets/ui/webfonts/iosevka-latin-900-normal.woff2", "res://assets/ui/webfonts/iosevka-latin-900-normal.woff", "res://assets/ui/webfonts/iosevka-latin-900-italic.woff2", "res://assets/ui/webfonts/iosevka-latin-900-italic.woff", "res://assets/ui/webfonts/iosevka-latin-800-normal.woff2", "res://assets/ui/webfonts/iosevka-latin-800-normal.woff", "res://assets/ui/webfonts/iosevka-latin-800-italic.woff2", "res://assets/ui/webfonts/iosevka-latin-800-italic.woff", "res://assets/ui/webfonts/iosevka-latin-700-normal.woff2", "res://assets/ui/webfonts/iosevka-latin-700-normal.woff", "res://assets/ui/webfonts/iosevka-latin-700-italic.woff2", "res://assets/ui/webfonts/iosevka-latin-700-italic.woff", "res://assets/ui/webfonts/iosevka-latin-600-normal.woff2", "res://assets/ui/webfonts/iosevka-latin-600-normal.woff", "res://assets/ui/webfonts/iosevka-latin-600-italic.woff2", "res://assets/ui/webfonts/iosevka-latin-600-italic.woff", "res://assets/ui/webfonts/iosevka-latin-500-normal.woff2", "res://assets/ui/webfonts/iosevka-latin-500-normal.woff", "res://assets/ui/webfonts/iosevka-latin-500-italic.woff2", "res://assets/ui/webfonts/iosevka-latin-500-italic.woff", "res://assets/ui/webfonts/iosevka-latin-400-normal.woff2", "res://assets/ui/webfonts/iosevka-latin-400-normal.woff", "res://assets/ui/webfonts/iosevka-latin-400-italic.woff2", "res://assets/ui/webfonts/iosevka-latin-400-italic.woff", "res://assets/ui/webfonts/iosevka-latin-300-normal.woff2", "res://assets/ui/webfonts/iosevka-latin-300-normal.woff", "res://assets/ui/webfonts/iosevka-latin-300-italic.woff2", "res://assets/ui/webfonts/iosevka-latin-300-italic.woff", "res://assets/ui/webfonts/iosevka-latin-200-normal.woff2", "res://assets/ui/webfonts/iosevka-latin-200-normal.woff", "res://assets/ui/webfonts/iosevka-latin-200-italic.woff2", "res://assets/ui/webfonts/iosevka-latin-200-italic.woff", "res://assets/ui/webfonts/iosevka-latin-100-normal.woff2", "res://assets/ui/webfonts/iosevka-latin-100-normal.woff", "res://assets/ui/webfonts/iosevka-latin-100-italic.woff2", "res://assets/ui/webfonts/iosevka-latin-100-italic.woff", "res://assets/snd/music/Thermistor.ogg", "res://assets/snd/music/Sadesper Record - Mad Pierrot.ogg", "res://assets/snd/music/Reflection.ogg", "res://assets/snd/music/Percussion Revolver.ogg", "res://assets/snd/music/PD_ShoppingMall.ogg", "res://assets/snd/music/neuralnetwork.ogg", "res://addons/blender_godot_pipeline/blender_godot_pipeline.gd", "res://addons/blender_godot_pipeline/GLTFImporter.gd", "res://addons/blender_godot_pipeline/icon.png", "res://addons/blender_godot_pipeline/SceneInit.gd", "res://addons/controller_visualizer/controller_visualizer.gd", "res://assets/player/player/textures/cel.gdshader", "res://assets/player/player/textures/blocky.png", "res://assets/player/player/textures/b8.png", "res://assets/env/sandbox_night.tres", "res://assets/env/sandbox.tres", "res://addons/deploy_to_steamos/folder.svg", "res://addons/deploy_to_steamos/icon.svg", "res://addons/flexible_toon_shader/example/cup.obj", "res://addons/flexible_toon_shader/example/CupMaterial.tres", "res://addons/flexible_toon_shader/example/CupMaterialHatch.tres", "res://addons/flexible_toon_shader/example/cup_specular.png", "res://addons/flexible_toon_shader/example/cup_texture.png", "res://assets/env/obby00.tres", "res://addons/flexible_toon_shader/FlexibleToonMaterial.tres", "res://addons/flexible_toon_shader/flexible_toon.gdshader", "res://addons/flexible_toon_shader/hatch.png", "res://addons/flexible_toon_shader/HatchToonMaterial.tres", "res://addons/flexible_toon_shader/hatch_toon.gdshader", "res://addons/Godot-MToon-Shader/inspector_mtoon.gd", "res://addons/Godot-MToon-Shader/mtoon.gdshader", "res://addons/Godot-MToon-Shader/mtoon_common.gdshaderinc", "res://addons/Godot-MToon-Shader/mtoon_cull_off.gdshader", "res://addons/Godot-MToon-Shader/mtoon_cutout.gdshader", "res://addons/Godot-MToon-Shader/mtoon_cutout_cull_off.gdshader", "res://addons/Godot-MToon-Shader/mtoon_outline.gdshader", "res://addons/Godot-MToon-Shader/mtoon_outline_cutout.gdshader", "res://addons/Godot-MToon-Shader/mtoon_outline_trans.gdshader", "res://addons/Godot-MToon-Shader/mtoon_outline_trans_zwrite.gdshader", "res://addons/Godot-MToon-Shader/mtoon_trans.gdshader", "res://addons/Godot-MToon-Shader/mtoon_trans_cull_off.gdshader", "res://addons/Godot-MToon-Shader/mtoon_trans_zwrite.gdshader", "res://addons/Godot-MToon-Shader/mtoon_trans_zwrite_cull_off.gdshader", "res://addons/Godot-MToon-Shader/plugin.gd", "res://addons/godotfgd/main.gd", "res://addons/godotvmf/entities/func_instance.gd", "res://assets/env/gloomy.tres", "res://addons/godotvmf/entities/prop_static.gd", "res://addons/SunshineVolumetricClouds/WorldShadows/CloudsShadowMaterialTest.tres", "res://addons/godotvmf/entities/prop_studio.gd", "res://addons/godotvmf/godotmdl/ani_reader.gd", "res://addons/godotvmf/godotmdl/import.gd", "res://addons/godotvmf/godotmdl/mdl_reader.gd", "res://addons/godotvmf/godotmdl/mesh_generator.gd", "res://addons/godotvmf/godotmdl/phy_reader.gd", "res://addons/godotvmf/godotmdl/reader.gd", "res://addons/godotvmf/godotmdl/vtx_reader.gd", "res://addons/godotvmf/godotmdl/vvd_reader.gd", "res://addons/godotvmf/godotvdf/vdf_parser.gd", "res://addons/godotvmf/godotvmt/vmt_context_menu.gd", "res://addons/godotvmf/godotvmt/vmt_import.gd", "res://addons/godotvmf/godotvmt/vmt_loader.gd", "res://addons/godotvmf/godotvmt/vmt_shader_based_material.gd", "res://addons/godotvmf/godotvmt/vtf_import.gd", "res://addons/godotvmf/godotvmt/vtf_loader.gd", "res://addons/godotvmf/shaders/WorldVertexTransitionMaterial.gd", "res://addons/godotvmf/shaders/WorldVertexTransitionMaterial.gdshader", "res://addons/godotvmf/src/ValveIONode.gd", "res://addons/godotvmf/src/VMFConfig.gd", "res://addons/godotvmf/src/VMFDispTool.gd", "res://addons/godotvmf/src/VMFInstanceManager.gd", "res://addons/godotvmf/src/VMFLogger.gd", "res://addons/godotvmf/src/VMFNode.gd", "res://addons/godotvmf/src/VMFTool.gd", "res://addons/godotvmf/godotvmf.gd", "res://addons/godotvmf/hammer.png", "res://addons/godotvmf/icon.svg", "res://addons/SunshineVolumetricClouds/WorldShadows/CloudsShadowsExample.gdshader", "res://addons/godotvmf/ui.gd", "res://addons/godotvmf/utils.gd", "res://addons/godotvmtsync/main.gd", "res://addons/godotvmtsync/vmt_watcher.gd", "res://addons/GoldGdt/src/FootstepManager.gd", "res://addons/GoldGdt/src/gdticon.png", "res://addons/GoldGdt/src/GoldGdt_Body.gd", "res://addons/GoldGdt/src/GoldGdt_Camera.gd", "res://addons/GoldGdt/src/GoldGdt_Controls.gd", "res://addons/GoldGdt/src/GoldGdt_DebugUI.gd", "res://addons/GoldGdt/src/GoldGdt_Move.gd", "res://addons/GoldGdt/src/GoldGdt_Pawn.gd", "res://addons/GoldGdt/src/GoldGdt_View.gd", "res://addons/GoldGdt/src/PlayerParameters.gd", "res://addons/GoldGdt/Default.tres", "res://assets/effect/bullethole.png", "res://addons/GoldGdt/goldgdt_plugin.gd", "res://addons/SunshineVolumetricClouds/AltLightingRayMarchedCloudsPost.gdshader", "res://addons/grappling_hook_3d/example/hook_availible.png", "res://addons/grappling_hook_3d/example/hook_not_availible.png", "res://addons/SunshineVolumetricClouds/BaseNoiseDetailTexture.tres", "res://addons/grappling_hook_3d/example/moving_box.gd", "res://addons/grappling_hook_3d/example/player.gd", "res://assets/effect/particles/aware_particlemesh.tres", "res://addons/grappling_hook_3d/src/charge_indicator.gd", "res://assets/effect/particles/aware_material.tres", "res://addons/grappling_hook_3d/src/charge_indicatorv2.gd", "res://addons/SunshineVolumetricClouds/BaseNoiseLargeScaleTexture.tres", "res://addons/grappling_hook_3d/src/charge_indicator_backup.gd", "res://addons/SunshineVolumetricClouds/SunshineIcon.svg", "res://addons/grappling_hook_3d/src/hook_controller.gd", "res://addons/grappling_hook_3d/src/hook_model.gd", "res://addons/grappling_hook_3d/plugin.gd", "res://addons/grappling_hook_3d/speed_distance_curve.tres", "res://addons/grappling_hook_3d/speed_hook_speed_curve.tres", "res://addons/health_hitbox_hurtbox/2d/hit_box_2d/basic_hit_box_2d.gd", "res://addons/health_hitbox_hurtbox/2d/hit_box_2d/hit_box_2d.gd", "res://addons/health_hitbox_hurtbox/2d/hit_box_2d/hit_box_2d.svg", "res://addons/health_hitbox_hurtbox/2d/hit_scan_2d/basic_hit_scan_2d.gd", "res://addons/health_hitbox_hurtbox/2d/hit_scan_2d/hit_scan_2d.gd", "res://addons/health_hitbox_hurtbox/2d/hit_scan_2d/hit_scan_2d.svg", "res://addons/health_hitbox_hurtbox/2d/hurt_box_2d/basic_hurt_box_2d.gd", "res://addons/health_hitbox_hurtbox/2d/hurt_box_2d/hurt_box_2d.gd", "res://addons/health_hitbox_hurtbox/2d/hurt_box_2d/hurt_box_2d.svg", "res://addons/health_hitbox_hurtbox/3d/hit_box_3d/basic_hit_box_3d.gd", "res://addons/health_hitbox_hurtbox/3d/hit_box_3d/hit_box_3d.gd", "res://addons/health_hitbox_hurtbox/3d/hit_box_3d/hit_box_3d.svg", "res://addons/health_hitbox_hurtbox/3d/hit_scan_3d/basic_hit_scan_3d.gd", "res://addons/health_hitbox_hurtbox/3d/hit_scan_3d/hit_scan_3d.gd", "res://addons/health_hitbox_hurtbox/3d/hit_scan_3d/hit_scan_3d.svg", "res://addons/health_hitbox_hurtbox/3d/hurt_box_3d/basic_hurt_box_3d.gd", "res://addons/health_hitbox_hurtbox/3d/hurt_box_3d/hurt_box_3d.gd", "res://addons/health_hitbox_hurtbox/3d/hurt_box_3d/hurt_box_3d.svg", "res://addons/health_hitbox_hurtbox/health/health.gd", "res://addons/health_hitbox_hurtbox/health/health.svg", "res://addons/health_hitbox_hurtbox/resources/action.gd", "res://addons/health_hitbox_hurtbox/resources/action_type.gd", "res://addons/health_hitbox_hurtbox/resources/modified_action.gd", "res://addons/health_hitbox_hurtbox/resources/modifier.gd", "res://addons/health_hitbox_hurtbox/plugin.gd", "res://addons/icons-fonts/examples/rich_text_label_with_icons.gd", "res://addons/SunshineVolumetricClouds/SunshineCloudsInstaller.gd", "res://addons/icons-fonts/icons_fonts/emojis/emojis.json", "res://addons/icons-fonts/icons_fonts/emojis/NotoColorEmoji.ttf", "res://addons/icons-fonts/icons_fonts/MaterialIcons/icons.json", "res://addons/icons-fonts/icons_fonts/MaterialIcons/material_design_icons.ttf", "res://addons/icons-fonts/icons_fonts/IconsFonts.gd", "res://addons/SunshineVolumetricClouds/RayMarchedCloudsPost.gdshader", "res://addons/SunshineVolumetricClouds/HeightWeightGradient.tres", "res://addons/icons-fonts/icon_finder/IconsFontsRender.gd", "res://addons/icons-fonts/icon_finder/icon_finder.gd", "res://addons/icons-fonts/icon_finder/icon_finder_window.gd", "res://addons/icons-fonts/inspector/font_icon_inspector.gd", "res://addons/icons-fonts/nodes/FontIcon.gd", "res://addons/icons-fonts/nodes/FontIcon.svg", "res://addons/icons-fonts/nodes/FontIconButton.gd", "res://addons/icons-fonts/nodes/FontIconButton.svg", "res://addons/icons-fonts/nodes/FontIconCheckButton.gd", "res://addons/icons-fonts/resources/FontIconSetting.gd", "res://addons/icons-fonts/resources/FontIconSettings.svg", "res://addons/icons-fonts/icons_fonts.gd", "res://addons/inspector_tabs/icon_grabber.gd", "res://addons/inspector_tabs/inspector_tabs.gd", "res://scenes/ui/main_menu.tscn")
include_filter=""
exclude_filter=""
export_path="build/test/stiletto.html"
patches=PackedStringArray()
encryption_include_filters=""
encryption_exclude_filters=""
seed=0
encrypt_pck=false
encrypt_directory=false
script_export_mode=2

[preset.1.options]

custom_template/debug=""
custom_template/release=""
variant/extensions_support=true
variant/thread_support=true
vram_texture_compression/for_desktop=true
vram_texture_compression/for_mobile=false
html/export_icon=true
html/custom_html_shell="res://build/test/stiletto - Copy.html"
html/head_include=""
html/canvas_resize_policy=2
html/focus_canvas_on_start=true
html/experimental_virtual_keyboard=false
progressive_web_app/enabled=false
progressive_web_app/ensure_cross_origin_isolation_headers=true
progressive_web_app/offline_page=""
progressive_web_app/display=1
progressive_web_app/orientation=0
progressive_web_app/icon_144x144=""
progressive_web_app/icon_180x180=""
progressive_web_app/icon_512x512=""
progressive_web_app/background_color=Color(0, 0, 0, 1)
export/split_pcks=false
