[PCK_splits]

res://maps="maps.pck"
res://AudioMixerVisualizer="AudioMixerVisualizer.pck"
res://Demo="Demo.pck"
res://Screenshots="Screenshots.pck"
res://build="build.pck"
res://dlcs="dlcs.pck"
res://fps_framework="fps_framework.pck"
res://materials/models/props_se="materials/models/props_se.pck"
res://metroidprime="metroidprime.pck"
res://sourcesdk_reference="sourcesdk_reference.pck"
res://test="test.pck"
res://vmf="vmf.pck"

[settings]

keep_full_pck=false
