<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fullscreen Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #333;
            color: white;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-viewport {
            width: 400px;
            height: 300px;
            background: #000;
            border: 2px solid #555;
            margin: 20px auto;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .fullscreen-btn {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            border-radius: 4px;
            margin: 10px;
        }
        
        .fullscreen-btn:hover {
            background: #005a9e;
        }
        
        /* Fullscreen styles - same as stiletto.html */
        :fullscreen .test-container,
        :-webkit-full-screen .test-container,
        :-moz-full-screen .test-container,
        :-ms-fullscreen .test-container {
            padding: 0 !important;
            justify-content: center !important;
        }

        :fullscreen .test-viewport,
        :-webkit-full-screen .test-viewport,
        :-moz-full-screen .test-viewport,
        :-ms-fullscreen .test-viewport {
            height: 100vh !important;
            width: 100vw !important;
            max-height: none !important;
            max-width: none !important;
            margin: 0 !important;
            border: none !important;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Fullscreen Test</h1>
        <p>This test verifies that fullscreen mode works correctly with edge-to-edge display.</p>
        
        <div class="test-viewport" id="testViewport">
            <div>
                <h2>Test Viewport</h2>
                <p>This should fill the entire screen in fullscreen mode</p>
                <button class="fullscreen-btn" onclick="exitFullscreen()">Exit Fullscreen</button>
            </div>
        </div>
        
        <button class="fullscreen-btn" onclick="enterFullscreen()">Enter Fullscreen</button>
        <button class="fullscreen-btn" onclick="testF11()">Test F11 (Press F11 manually)</button>
        
        <div id="status"></div>
    </div>

    <script>
        function enterFullscreen() {
            const element = document.documentElement;
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            }
        }
        
        function exitFullscreen() {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
        }
        
        function testF11() {
            document.getElementById('status').innerHTML = '<p>Press F11 to test fullscreen. The viewport should fill the entire screen with no borders.</p>';
        }
        
        // Monitor fullscreen changes
        document.addEventListener('fullscreenchange', updateStatus);
        document.addEventListener('webkitfullscreenchange', updateStatus);
        document.addEventListener('mozfullscreenchange', updateStatus);
        document.addEventListener('MSFullscreenChange', updateStatus);
        
        function updateStatus() {
            const isFullscreen = !!(document.fullscreenElement || document.webkitFullscreenElement || 
                                   document.mozFullScreenElement || document.msFullscreenElement);
            const status = document.getElementById('status');
            if (isFullscreen) {
                status.innerHTML = '<p style="color: #00ff00;">✓ Fullscreen active - viewport should fill entire screen edge-to-edge</p>';
            } else {
                status.innerHTML = '<p style="color: #ffaa00;">Windowed mode</p>';
            }
        }
    </script>
</body>
</html>
